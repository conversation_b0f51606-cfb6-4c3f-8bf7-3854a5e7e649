# 开发环境配置

# 应用信息
VITE_APP_TITLE=三角洲数据查询攻略站
VITE_APP_VERSION=1.0.2
VITE_APP_DESCRIPTION=专业的游戏物品数据分析平台

# API接口配置
VITE_API_BASE_URL=http://localhost:8000/index.php/api/
VITE_API_TIMEOUT=20000
VITE_API_RETRY_COUNT=3
VITE_API_RETRY_DELAY=1000
VITE_API_MAX_RETRY_DELAY=10000
VITE_API_RETRY_MULTIPLIER=2

# 缓存配置
VITE_ENABLE_CACHE=true
VITE_CACHE_EXPIRE_TIME=3600000
VITE_CACHE_PREFIX=api_cache_

# 调试配置
VITE_DEBUG=true
VITE_LOG_LEVEL=debug
VITE_SHOW_API_LOGS=true

# Mock数据配置
VITE_USE_MOCK=false
VITE_MOCK_DELAY=500

# 客户端配置
VITE_CLIENT_TYPE=web
VITE_CLIENT_VERSION=1.0.2
