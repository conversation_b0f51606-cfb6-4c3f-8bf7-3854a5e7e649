<template>
  <div class="price-history-container">
    <!-- 统计卡片 - 与老版本保持一致的7个统计项 -->
    <div class="statistics-cards">
      <n-grid :cols="4" :x-gap="12" class="stat-cards">
        <n-grid-item>
          <div class="stat-card stat-min">
            <div class="stat-label">最低哈夫币</div>
            <div class="stat-value lowest">
              <TransitionNumber :value="statistics.lowestPrice" :format="formatPrice" />
            </div>
          </div>
        </n-grid-item>
        <n-grid-item>
          <div class="stat-card stat-max">
            <div class="stat-label">最高哈夫币</div>
            <div class="stat-value highest">
              <TransitionNumber :value="statistics.highestPrice" :format="formatPrice" />
            </div>
          </div>
        </n-grid-item>
        <n-grid-item>
          <div class="stat-card stat-avg">
            <div class="stat-label">平均哈夫币</div>
            <div class="stat-value average">
              <TransitionNumber :value="statistics.averagePrice" :format="formatPrice" />
            </div>
          </div>
        </n-grid-item>
        <n-grid-item>
          <div class="stat-card stat-change">
            <div class="stat-label">变化值</div>
            <div class="stat-value" :class="changeClass">
              <TransitionNumber :value="statistics.changeValue" :format="formatPrice" />
            </div>
          </div>
        </n-grid-item>
      </n-grid>

      <n-grid :cols="3" :x-gap="12" class="stat-cards secondary-stats">
        <n-grid-item>
          <div class="stat-card stat-start">
            <div class="stat-label">起始哈夫币</div>
            <div class="stat-value start">
              <TransitionNumber :value="statistics.startPrice" :format="formatPrice" />
            </div>
          </div>
        </n-grid-item>
        <n-grid-item>
          <div class="stat-card stat-end">
            <div class="stat-label">结束哈夫币</div>
            <div class="stat-value end">
              <TransitionNumber :value="statistics.endPrice" :format="formatPrice" />
            </div>
          </div>
        </n-grid-item>
        <n-grid-item>
          <div class="stat-card stat-percent">
            <div class="stat-label">变化率</div>
            <div class="stat-value" :class="changeClass">
              {{ statistics.changePercent >= 0 ? '+' : '' }}{{ statistics.changePercent.toFixed(2) }}%
            </div>
          </div>
        </n-grid-item>
      </n-grid>
    </div>

    <!-- 时间段选择 -->
    <div class="period-selector">
      <n-space align="center">
        <n-button-group>
          <n-button
            v-for="period in chartPeriods"
            :key="period.value"
            :type="selectedPeriod === period.value ? 'primary' : 'default'"
            size="small"
            @click="changePeriod(period.value)"
          >
            {{ period.label }}
          </n-button>
        </n-button-group>

        <!-- 时间过滤按钮 -->
        <n-popover
          trigger="click"
          placement="bottom-end"
          :show="showTimeFilter"
          @update:show="showTimeFilter = $event"
          style="max-width: 400px;"
        >
          <template #trigger>
            <n-button
              size="small"
              type="tertiary"
              :secondary="selectedTimeFilter.length > 0"
            >
              <template #icon>
                <n-icon><svg viewBox="0 0 24 24"><path fill="currentColor" d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10s10-4.5 10-10S17.5 2 12 2M7.7 15.5L7 14.2l3-1.7V7h1.5v5.8l-3.8 2.7Z"/></svg></n-icon>
              </template>
              时间过滤
              <span v-if="selectedTimeFilter.length > 0" style="margin-left: 4px;">
                ({{ selectedTimeFilter.length }})
              </span>
            </n-button>
          </template>

          <!-- 时间过滤面板内容 -->
          <div class="time-filter-panel">
            <div class="time-filter-header">
              <span class="panel-title">选择显示时间点</span>
              <n-space size="small">
                <n-button size="tiny" @click="selectAllHours" type="success" ghost>全选</n-button>
                <n-button size="tiny" @click="clearAllHours" type="warning" ghost>清空</n-button>
                <n-button size="tiny" @click="invertSelection" type="info" ghost>反选</n-button>
              </n-space>
            </div>

            <!-- 预设选项 -->
            <div class="time-filter-presets">
              <span class="preset-label">快速选择：</span>
              <n-space size="small" wrap>
                <n-tag
                  v-for="preset in timeFilterPresets"
                  :key="preset.label"
                  :type="arraysEqual(selectedTimeFilter, preset.value) ? 'primary' : 'default'"
                  checkable
                  :checked="arraysEqual(selectedTimeFilter, preset.value)"
                  @click="applyPreset(preset.value)"
                  size="small"
                  class="preset-tag"
                >
                  {{ preset.label }}
                </n-tag>
              </n-space>
            </div>

            <!-- 小时选择网格 -->
            <div class="time-filter-hours">
              <div class="hours-grid">
                <div
                  v-for="hour in hourOptions"
                  :key="hour.value"
                  class="hour-checkbox-wrapper"
                  :class="{ 'hour-selected': selectedTimeFilter.includes(hour.value) }"
                  @click="toggleHour(hour.value, !selectedTimeFilter.includes(hour.value))"
                >
                  <n-checkbox
                    :checked="selectedTimeFilter.includes(hour.value)"
                    @update:checked="toggleHour(hour.value, $event)"
                    size="small"
                    @click.stop
                  >
                    {{ hour.label }}
                  </n-checkbox>
                </div>
              </div>
            </div>

            <!-- 底部状态和操作 -->
            <div class="time-filter-footer">
              <div class="filter-status">
                <span class="selected-count">
                  <n-icon style="margin-right: 4px;">
                    <svg viewBox="0 0 24 24"><path fill="currentColor" d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10s10-4.5 10-10S17.5 2 12 2M7.7 15.5L7 14.2l3-1.7V7h1.5v5.8l-3.8 2.7Z"/></svg>
                  </n-icon>
                  已选择 {{ selectedTimeFilter.length }}/24 个时间点
                </span>
                <span v-if="selectedTimeFilter.length === 0" class="filter-hint">
                  (未选择将显示全部时间点)
                </span>
                <span v-else-if="selectedTimeFilter.length < 24" class="filter-hint">
                  (仅显示选中时间的数据)
                </span>
              </div>
              <n-space size="small">
                <n-button size="small" @click="resetTimeFilter" :disabled="selectedTimeFilter.length === 0">
                  重置
                </n-button>
                <n-button size="small" type="primary" @click="applyTimeFilter">
                  <template #icon>
                    <n-icon><svg viewBox="0 0 24 24"><path fill="currentColor" d="M9 16.17L4.83 12l-1.42 1.41L9 19L21 7l-1.41-1.41z"/></svg></n-icon>
                  </template>
                  确认
                </n-button>
              </n-space>
            </div>
          </div>
        </n-popover>
      </n-space>
    </div>

    <!-- 图表容器 -->
    <div class="chart-container" @click="handleChartClick" :class="{ clickable: !props.enlarge }">
      <n-spin :show="loading">
        <div ref="chartRef" :style="{ height: chartHeight + 'px' }"></div>
      </n-spin>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { NGrid, NGridItem, NSpin, NButton, NButtonGroup, NPopover, NIcon, NTag, NCheckbox, NSpace, useMessage } from 'naive-ui'
import * as echarts from 'echarts'
import { useThemeStore } from '@/stores/theme'
import { UserPreferences } from '@/utils/storage'
import { getPriceHistory as apiGetPriceHistory } from '@/api/items'
import TransitionNumber from '@/components/TransitionNumber.vue'
import { debounce } from 'lodash-es'

// 定义事件
const emit = defineEmits<{
  chartClick: []
}>()

// ECharts 已经包含所有组件，无需单独注册

// Props - 与老版本保持一致
interface Props {
  objectId: number
  currentPrice: number
  price24hAgo: number
  grade: number
  loading?: boolean
  enlarge?: boolean
  mobile?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  enlarge: false,
  mobile: false
})

// 响应式数据
const chartRef = ref<HTMLDivElement>()
const chart = ref<echarts.ECharts>()
const internalLoading = ref(false)
const showTimeFilter = ref(false)

// 精细的加载状态管理
const chartLoading = ref(false)
const chartRenderComplete = ref(false)
const loadingHideTimer = ref<number | null>(null)
const selectedHours = computed({
  get: () => selectedTimeFilter.value,
  set: (value) => {
    selectedTimeFilter.value = value
  }
})

// 合并内部loading和props loading以及图表loading
const loading = computed(() => props.loading || internalLoading.value || chartLoading.value)

// 主题和消息
const themeStore = useThemeStore()
const message = useMessage()
const isDarkTheme = computed(() => themeStore.isDark)

// 原始数据和统计数据
const rawData = ref<any[]>([])
const rawHistoryData = ref<any[]>([])
const processedData = ref<any[]>([])
const statisticsData = ref<any>({})

// 性能优化相关
const isUpdating = ref(false)
const updateTimer = ref<number | null>(null)
const processedDataCache = ref<any[]>([])
const lastDataHash = ref('')

// 性能监控
const performanceMetrics = ref({
  lastUpdateTime: 0,
  dataProcessingTime: 0,
  chartRenderTime: 0,
  networkRequestTime: 0,
  totalUpdateTime: 0,
  cacheHitCount: 0,
  cacheMissCount: 0
})

// 从用户偏好中读取上次选择的时间段，默认为7天
const selectedPeriod = ref(UserPreferences.getChartPeriod('7d'))
// 从用户偏好中读取时间点过滤设置，默认为空数组（显示所有时间点）
const selectedTimeFilter = ref<number[]>(UserPreferences.getChartTimeFilter([]))

// 时间段配置
const chartPeriods = [
  { label: '24小时', value: '24h' },
  { label: '3天', value: '3d' },
  { label: '7天', value: '7d' },
  { label: '30天', value: '30d' },
  { label: '90天', value: '90d' },
  { label: '180天', value: '180d' }
]

// 时间点过滤预设选项
const timeFilterPresets = [
  { label: '全部时间', value: [] },
  { label: '工作时间', value: [9, 10, 11, 12, 13, 14, 15, 16, 17, 18] },
  { label: '交易活跃', value: [10, 14, 16, 20, 22] },
  { label: '夜间时段', value: [0, 1, 2, 3, 4, 5, 6] },
  { label: '整点时间', value: [0, 6, 12, 18] }
]

// 生成24小时选项
const hourOptions = Array.from({ length: 24 }, (_, i) => ({
  label: `${i.toString().padStart(2, '0')}:00`,
  value: i
}))

// 等级颜色
const gradeColors: Record<number, string> = {
  1: '#949595', // 灰
  2: '#188D14', // 绿
  3: '#658BCE', // 蓝
  4: '#9b61c8', // 紫
  5: '#e8a64e', // 橙
  6: '#cb464a'  // 红
}

// 图表高度
const chartHeight = computed(() => {
  if (props.mobile) return 300
  if (props.enlarge) return 400
  return 300
})

// 统计数据 - 优先使用API返回的统计数据，如果没有则计算
const statistics = computed(() => {
  // 如果有API返回的统计数据，直接使用
  if (statisticsData.value && Object.keys(statisticsData.value).length > 0) {
    return {
      lowestPrice: statisticsData.value.min_price || 0,
      highestPrice: statisticsData.value.max_price || 0,
      averagePrice: statisticsData.value.avg_price || 0,
      startPrice: statisticsData.value.start_price || 0,
      endPrice: statisticsData.value.end_price || 0,
      changeValue: statisticsData.value.change_value || 0,
      changePercent: statisticsData.value.change_percent || 0
    }
  }

  // 如果没有API统计数据，从处理后的数据计算
  if (!processedData.value.length) {
    return {
      lowestPrice: 0,
      highestPrice: 0,
      averagePrice: 0,
      startPrice: 0,
      endPrice: 0,
      changeValue: 0,
      changePercent: 0
    }
  }

  const prices = processedData.value.map(item => item.price)
  const lowestPrice = Math.min(...prices)
  const highestPrice = Math.max(...prices)
  const averagePrice = prices.reduce((sum, price) => sum + price, 0) / prices.length
  const startPrice = prices[0]
  const endPrice = prices[prices.length - 1]
  const changeValue = endPrice - startPrice
  const changePercent = startPrice > 0 ? (changeValue / startPrice) * 100 : 0

  return {
    lowestPrice,
    highestPrice,
    averagePrice,
    startPrice,
    endPrice,
    changeValue,
    changePercent
  }
})

// 变化样式类 - 根据用户偏好：红色表示上涨，绿色表示下跌
const changeClass = computed(() => {
  if (statistics.value.changeValue > 0) return 'increase'
  if (statistics.value.changeValue < 0) return 'decrease'
  return 'neutral'
})

// 智能价格格式化函数 - 与老版本保持一致
const formatPrice = (price: number, options: {
  forceUnit?: boolean,
  maxDecimals?: number,
  minDecimals?: number
} = {}) => {
  const { forceUnit = false, maxDecimals = 2, minDecimals = 0 } = options

  if (price === 0) return '0'

  const absPrice = Math.abs(price)

  // 小于1000的价格直接显示，不转换单位
  if (absPrice < 1000 && !forceUnit) {
    // 智能小数位数：如果是整数就不显示小数
    const isInteger = price % 1 === 0
    if (isInteger && minDecimals === 0) {
      return price.toString()
    }
    return price.toFixed(Math.max(minDecimals, isInteger ? 0 : Math.min(maxDecimals, 2)))
  }

  // 1000-9999的价格，根据情况决定是否转换
  if (absPrice < 10000 && !forceUnit) {
    const isInteger = price % 1 === 0
    if (isInteger && minDecimals === 0) {
      return price.toString()
    }
    return price.toFixed(Math.max(minDecimals, isInteger ? 0 : Math.min(maxDecimals, 2)))
  }

  // 10000以上转换为万单位
  const wanPrice = price / 10000
  const isInteger = wanPrice % 1 === 0

  if (isInteger && minDecimals === 0) {
    return `${wanPrice}W`
  }

  return `${wanPrice.toFixed(Math.max(minDecimals, isInteger ? 0 : Math.min(maxDecimals, 2)))}W`
}

// Y轴智能格式化 - 根据价格范围决定是否使用万单位
const formatYAxisPrice = (price: number) => {
  return formatPrice(price, { forceUnit: false, maxDecimals: 2 })
}

// 性能监控函数
const trackPerformance = (operation: string, startTime: number) => {
  const endTime = performance.now()
  const duration = endTime - startTime

  switch (operation) {
    case 'dataProcessing':
      performanceMetrics.value.dataProcessingTime = duration
      break
    case 'chartRender':
      performanceMetrics.value.chartRenderTime = duration
      break
    case 'networkRequest':
      performanceMetrics.value.networkRequestTime = duration
      break
    case 'totalUpdate':
      performanceMetrics.value.totalUpdateTime = duration
      break
  }

  // 开发环境下的性能警告
  if (import.meta.env.DEV) {
    const thresholds = {
      dataProcessing: 50,
      chartRender: 100,
      networkRequest: 2000,
      totalUpdate: 3000
    }

    const threshold = thresholds[operation as keyof typeof thresholds] || 100

    if (duration > threshold) {
      console.warn(`[性能警告] ${operation} 耗时过长: ${duration.toFixed(2)}ms (阈值: ${threshold}ms)`)
    }
  }
}

// 数据验证函数
const validateStatisticsData = () => {
  if (!statisticsData.value || Object.keys(statisticsData.value).length === 0) {
    return { valid: true, warnings: [] }
  }

  const warnings: string[] = []
  const data = statisticsData.value

  // 检查价格数据的合理性
  if (data.avg_price && (data.avg_price < 0 || data.avg_price > 100000000)) {
    warnings.push(`平均价格异常: ${data.avg_price}`)
  }

  if (data.min_price && data.max_price && data.min_price > data.max_price) {
    warnings.push(`最低价格(${data.min_price})大于最高价格(${data.max_price})`)
  }

  // 检查变化百分比的合理性
  if (Math.abs(data.change_percent || 0) > 1000) {
    warnings.push(`价格变化百分比异常: ${data.change_percent}%`)
  }

  if (import.meta.env.DEV && warnings.length > 0) {
    console.group('[数据验证] 发现统计数据异常')
    warnings.forEach(warning => console.warn(warning))
    console.log('原始统计数据:', data)
    console.groupEnd()
  }

  return { valid: warnings.length === 0, warnings }
}

// 生成数据哈希用于缓存比较
function generateDataHash(data: any[]): string {
  if (!data.length) return ''
  return `${data.length}-${data[0]?.time || ''}-${data[data.length - 1]?.time || ''}`
}

// 智能加载状态管理
const hideLoadingWithDelay = () => {
  // 清除之前的定时器
  if (loadingHideTimer.value) {
    clearTimeout(loadingHideTimer.value)
  }

  // 等待图表完全渲染后延迟500ms移除加载遮罩
  loadingHideTimer.value = setTimeout(() => {
    chartLoading.value = false
    chartRenderComplete.value = true

    if (import.meta.env.DEV) {
      console.log('[加载状态] 图表渲染完成，加载遮罩已移除')
    }
  }, 500) as any
}

// 获取价格历史数据 - 集成真实API和性能优化
const getPriceHistory = async () => {
  if (!props.objectId) return
  
  const networkStartTime = performance.now()
  const totalStartTime = performance.now()
  
  internalLoading.value = true
  try {
    const response = await apiGetPriceHistory(props.objectId, selectedPeriod.value)
    
    // 网络请求性能监控
    trackPerformance('networkRequest', networkStartTime)
    
    if (response.code === 1 && response.data && (response.data as any).history) {
      const newHistory = (response.data as any).history
      const newStatistics = (response.data as any).statistics || {}
      
      // 检查数据是否真的变化了
      const newDataHash = generateDataHash(newHistory)
      if (newDataHash !== lastDataHash.value) {
        rawHistoryData.value = newHistory
        rawData.value = newHistory
        statisticsData.value = newStatistics
        
        // 数据验证
        const validation = validateStatisticsData()
        if (!validation.valid && import.meta.env.DEV) {
          console.warn('[数据验证] 统计数据存在异常，但继续处理')
        }
        
        // 清理缓存
        processedDataCache.value = []
        lastDataHash.value = newDataHash
        
        processChartData()
        
        // 总体性能监控
        trackPerformance('totalUpdate', totalStartTime)
      }
    } else {
      rawHistoryData.value = []
      rawData.value = []
      statisticsData.value = {}
      processedData.value = []
      lastDataHash.value = ''
      processedDataCache.value = []
      message.error('获取价格历史数据失败')
    }
  } catch (error) {
    console.error('获取价格历史失败:', error)
    rawHistoryData.value = []
    rawData.value = []
    statisticsData.value = {}
    processedData.value = []
    lastDataHash.value = ''
    processedDataCache.value = []
    message.error('获取价格历史数据失败')
  } finally {
    internalLoading.value = false
  }
}

// 生成模拟数据
const generateMockData = () => {
  const data = []
  const now = new Date()
  const basePrice = 1000 + Math.random() * 5000
  
  for (let i = 0; i < 100; i++) {
    const time = new Date(now.getTime() - (100 - i) * 60 * 60 * 1000)
    const price = basePrice + (Math.random() - 0.5) * 1000
    data.push({
      time: time.toISOString(),
      price: Math.max(100, price)
    })
  }
  
  return data
}

// 处理图表数据 - 根据时间过滤和数据转换，添加缓存和性能监控
const processChartData = () => {
  const startTime = performance.now()
  
  if (!rawData.value.length) {
    processedData.value = []
    return
  }

  // 检查缓存
  const dataHash = generateDataHash(rawData.value)
  const filterHash = selectedTimeFilter.value.join(',')
  const cacheKey = `${dataHash}-${filterHash}`
  
  if (processedDataCache.value.length > 0 && lastDataHash.value === cacheKey) {
    processedData.value = processedDataCache.value
    performanceMetrics.value.cacheHitCount++
    trackPerformance('dataProcessing', startTime)
    updateChart()
    return
  }

  performanceMetrics.value.cacheMissCount++

  // 根据选中的小时过滤数据（如果有设置时间过滤）
  let filtered = rawData.value
  if (selectedTimeFilter.value.length > 0) {
    filtered = rawData.value.filter(item => {
      if (typeof item.time === 'string') {
        const date = new Date(item.time.replace(' ', 'T'))
        const hour = date.getHours()
        return selectedTimeFilter.value.includes(hour)
      }
      return true
    })
  }

  // 转换数据格式以适配ECharts
  const processedResult = filtered.map(item => ({
    time: item.time,
    price: Number(item.price)
  }))

  // 更新缓存
  processedData.value = processedResult
  processedDataCache.value = processedResult
  lastDataHash.value = cacheKey

  trackPerformance('dataProcessing', startTime)
  updateChart()
}

    // 更新图表 - 参考原版ApexCharts的实用性设计
    const updateChart = () => {
      if (isUpdating.value) return
      if (!chart.value || !processedData.value.length) return

      const chartStartTime = performance.now()
      isUpdating.value = true

      try {
        const data = processedData.value.map(item => {
          // 格式化时间标签，和原版保持一致
          const date = new Date(item.time)
          const weekMap = ['日', '一', '二', '三', '四', '五', '六']
          const month = (date.getMonth() + 1).toString().padStart(2, '0')
          const day = date.getDate().toString().padStart(2, '0')
          const week = weekMap[date.getDay()]
          const hour = date.getHours().toString().padStart(2, '0')
          const minute = date.getMinutes().toString().padStart(2, '0')
          const label = `${month}月${day}日 ${week} ${hour}:${minute}`
          
          return {
            name: label,
            value: [item.time, item.price],
            label: label,
            rawTime: item.time
          }
        })

        // 主色调
        const primaryColor = gradeColors[props.grade] || '#658BCE'

        // 计算最高价和最低价
        const prices = data.map(d => d.value[1])
        const minPrice = Math.min(...prices)
        const maxPrice = Math.max(...prices)
        const maxPoint = data.find(d => d.value[1] === maxPrice)
        const minPoint = data.find(d => d.value[1] === minPrice)

        const option = {
      grid: {
        left: '3%',
        right: '4%',
        bottom: '8%',
        top: '15%',  // 增加顶部空间，避免最高价标记被遮挡
        containLabel: true
      },
      tooltip: {
        show: true,
        trigger: 'axis',  // 使用axis触发模式
        backgroundColor: 'rgba(50, 50, 50, 0.9)',
        borderColor: '#ddd',
        borderWidth: 1,
        borderRadius: 4,
        textStyle: {
          color: '#fff',
          fontSize: 13
        },
        padding: [8, 12],
        triggerOn: 'mousemove',
        enterable: false,
        confine: true,
        position: function(point: number[], params: any, dom: any, rect: any, size: any) {
          // 将tooltip显示在鼠标右侧
          return [point[0] + 10, point[1] - 30];
        },
        axisPointer: {
          type: 'line',
          snap: true,  // 自动吸附到最近的数据点
          lineStyle: {
            color: primaryColor,
            width: 1.5,
            type: 'dashed'
          },
          label: {
            show: false
          },
          handle: {
            show: false
          }
        },
        formatter: function(params: any) {
          // 确保params是数组且有元素
          if (!params || !Array.isArray(params) || params.length === 0) return '';
          
          // 获取第一个数据点
          const param = params[0];
          if (!param || !param.data) return '';
          
          const data = param.data;
          if (!data.label || !data.value || data.value.length < 2) return '';
          
          return '<div style="font-weight:bold">' + data.label + '</div>' +
                 '<div>价格: ' + formatPrice(data.value[1]) + '</div>';
        }
      },
      xAxis: {
        type: 'time',
        boundaryGap: false,
        axisLine: {
          lineStyle: {
            color: isDarkTheme.value ? '#555' : '#ddd'
          }
        },
        axisLabel: {
          formatter: function(value) {
            const date = new Date(value);
            return `${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getDate().toString().padStart(2, '0')}`;
          },
          color: isDarkTheme.value ? '#aaa' : '#666'
        },
        splitLine: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: isDarkTheme.value ? '#555' : '#ddd'
          }
        },
        axisLabel: {
          formatter: formatYAxisPrice,
          color: isDarkTheme.value ? '#aaa' : '#666'
        },
        splitLine: {
          lineStyle: {
            color: isDarkTheme.value ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)'
          }
        }
      },
      series: [
        {
          type: 'line',
          name: '价格',
          data: data,
          smooth: true,
          showSymbol: true,
          symbolSize: 6,
          itemStyle: {
            color: primaryColor,
            borderColor: '#fff',
            borderWidth: 1.5,
            shadowColor: 'rgba(0, 0, 0, 0.2)',
            shadowBlur: 2
          },
          emphasis: {
            scale: true,
            itemStyle: {
              symbolSize: 10,  // 鼠标悬停时放大圆点
              borderWidth: 2,
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          symbol: 'circle',  // 确保使用圆形符号
          lineStyle: {
            color: primaryColor,
            width: 3
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: echarts.color.modifyAlpha(primaryColor, 0.5) },
              { offset: 1, color: echarts.color.modifyAlpha(primaryColor, 0.05) }
            ])
          },
          markLine: {
            silent: true,
            symbol: 'none',
            animation: false,
            data: [],
            lineStyle: {
              color: primaryColor,
              width: 1,
              type: 'dashed',
              opacity: 0
            }
          }
        }
      ]
    }

    // 设置图表选项
    chart.value.setOption(option)

    // 标记图表渲染完成
    trackPerformance('chartRender', chartStartTime)
    hideLoadingWithDelay()
  } catch (error) {
    console.error('更新图表失败:', error)
    message.error('图表渲染失败')
  } finally {
    isUpdating.value = false
  }
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  // 销毁旧图表实例
  if (chart.value) {
    chart.value.dispose()
  }

  // 创建新图表实例
  chart.value = echarts.init(chartRef.value, isDarkTheme.value ? 'dark' : undefined)
  
  // 设置图表加载状态
  chartLoading.value = true
  chartRenderComplete.value = false

  // 添加图表点击事件
  chart.value.on('click', () => {
    if (!props.enlarge) {
      emit('chartClick')
    }
  })

  // 添加窗口大小变化监听
  const resizeHandler = debounce(() => {
    if (chart.value) {
      chart.value.resize()
    }
  }, 200)

  window.addEventListener('resize', resizeHandler)

  // 获取数据并更新图表
  getPriceHistory()

  // 返回清理函数
  return () => {
    window.removeEventListener('resize', resizeHandler)
    if (chart.value) {
      chart.value.dispose()
      chart.value = undefined
    }
  }
}

// 时间段切换
const changePeriod = (period: string) => {
  if (selectedPeriod.value === period) return
  
  selectedPeriod.value = period
  UserPreferences.setChartPeriod(period)
  
  // 重新获取数据
  getPriceHistory()
}

// 时间过滤相关函数
const selectAllHours = () => {
  selectedHours.value = Array.from({ length: 24 }, (_, i) => i)
}

const clearAllHours = () => {
  selectedHours.value = []
}

const invertSelection = () => {
  const allHours = Array.from({ length: 24 }, (_, i) => i)
  selectedHours.value = allHours.filter(h => !selectedHours.value.includes(h))
}

const toggleHour = (hour: number, checked: boolean) => {
  if (checked && !selectedHours.value.includes(hour)) {
    selectedHours.value = [...selectedHours.value, hour].sort((a, b) => a - b)
  } else if (!checked) {
    selectedHours.value = selectedHours.value.filter(h => h !== hour)
  }
}

const applyPreset = (hours: number[]) => {
  selectedHours.value = [...hours]
}

const resetTimeFilter = () => {
  selectedHours.value = []
}

const applyTimeFilter = () => {
  UserPreferences.setChartTimeFilter(selectedHours.value)
  showTimeFilter.value = false
  processChartData()
}

// 数组比较函数
const arraysEqual = (a: any[], b: any[]) => {
  if (a.length !== b.length) return false
  if (a.length === 0 && b.length === 0) return true
  return a.every((val, idx) => val === b[idx])
}

// 图表点击处理
const handleChartClick = () => {
  if (!props.enlarge) {
    emit('chartClick')
  }
}

// 生命周期钩子
onMounted(() => {
  nextTick(() => {
    const cleanup = initChart()
    
    // 监听主题变化
    watch(() => isDarkTheme.value, () => {
      if (cleanup) cleanup()
      nextTick(() => {
        initChart()
      })
    })
    
    // 监听objectId变化
    watch(() => props.objectId, () => {
      if (props.objectId) {
        getPriceHistory()
      }
    })
  })
})

onUnmounted(() => {
  if (chart.value) {
    chart.value.dispose()
    chart.value = undefined
  }
  
  if (loadingHideTimer.value) {
    clearTimeout(loadingHideTimer.value)
  }
  
  if (updateTimer.value) {
    clearTimeout(updateTimer.value)
  }
})
</script>

<style scoped>
.price-history-container {
  width: 100%;
  padding: 0;
}

.statistics-cards {
  margin-bottom: 16px;
}

.stat-cards {
  margin-bottom: 8px;
}

.secondary-stats {
  margin-top: 8px;
}

.stat-card {
  background-color: var(--card-color);
  border-radius: 8px;
  padding: 12px;
  box-shadow: var(--card-shadow);
  transition: all 0.3s ease;
}

.stat-label {
  font-size: 0.85rem;
  color: var(--text-color-secondary);
  margin-bottom: 4px;
}

.stat-value {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
}

.stat-value.increase {
  color: var(--price-up-color);
}

.stat-value.decrease {
  color: var(--price-down-color);
}

.stat-value.neutral {
  color: var(--text-color);
}

.stat-value.lowest {
  color: var(--price-down-color);
}

.stat-value.highest {
  color: var(--price-up-color);
}

.stat-value.average {
  color: var(--primary-color);
}

.period-selector {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  position: relative;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  background-color: var(--card-color);
  box-shadow: var(--card-shadow);
}

.chart-container.clickable {
  cursor: pointer;
}

/* 时间过滤面板样式 */
.time-filter-panel {
  width: 360px;
  max-width: 100%;
  padding: 16px;
}

.time-filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.panel-title {
  font-weight: 600;
  font-size: 14px;
}

.time-filter-presets {
  margin-bottom: 16px;
}

.preset-label {
  font-size: 13px;
  color: var(--text-color-secondary);
  margin-right: 8px;
}

.preset-tag {
  margin-bottom: 4px;
}

.hours-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 8px;
  margin-bottom: 16px;
}

.hour-checkbox-wrapper {
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.hour-selected {
  background-color: rgba(var(--primary-color-rgb), 0.1);
}

.hour-checkbox-wrapper:hover {
  background-color: rgba(var(--primary-color-rgb), 0.05);
}

.time-filter-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-status {
  font-size: 13px;
  display: flex;
  flex-direction: column;
}

.selected-count {
  display: flex;
  align-items: center;
}

.filter-hint {
  font-size: 12px;
  color: var(--text-color-secondary);
  margin-top: 2px;
}

@media (max-width: 768px) {
  .hours-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .time-filter-panel {
    width: 300px;
  }
}
</style>
