<template>
  <div class="test-container">
    <h2>ECharts 图表测试</h2>
    <p>测试说明：这个组件会显示模拟数据，鼠标悬停在图表上应该能看到 tooltip</p>

    <!-- 测试没有 objectId 的情况，会显示模拟数据 -->
    <ItemPriceHistoryECharts
      :objectId="0"
      :currentPrice="1500"
      :price24hAgo="1400"
      :grade="3"
      :loading="false"
      :enlarge="false"
      :mobile="false"
      @chartClick="handleChartClick"
    />

    <div style="margin-top: 20px;">
      <h3>调试信息</h3>
      <p>请打开浏览器开发者工具的控制台查看调试信息</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import ItemPriceHistoryECharts from './ItemPriceHistoryECharts.vue'

const handleChartClick = () => {
  console.log('图表被点击了')
}
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

h2 {
  margin-bottom: 20px;
  color: #333;
}

h3 {
  margin-top: 20px;
  color: #666;
}

p {
  color: #666;
  line-height: 1.5;
}
</style>
