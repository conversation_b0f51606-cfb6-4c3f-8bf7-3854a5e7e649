<template>
  <div class="test-container">
    <h2>ECharts 图表测试</h2>
    <ItemPriceHistoryECharts
      :objectId="12345"
      :currentPrice="1500"
      :price24hAgo="1400"
      :grade="3"
      :loading="false"
      :enlarge="false"
      :mobile="false"
      @chartClick="handleChartClick"
    />
  </div>
</template>

<script setup lang="ts">
import ItemPriceHistoryECharts from './ItemPriceHistoryECharts.vue'

const handleChartClick = () => {
  console.log('图表被点击了')
}
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

h2 {
  margin-bottom: 20px;
  color: #333;
}
</style>
